package com.xo.backend.mappers;

import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.model.dto.EventBrowseDTO;
import com.xo.backend.model.dto.EventDetailsDTO;
import com.xo.backend.model.dto.EventVenueDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", uses = {BookingTierMapper.class, MapperUtils.class})
public interface EventDetailsMapper {

    @Mapping(target = "id", source = "eventEntity.id")
    @Mapping(target = "displayPrice", source = "eventEntity.displayPrice")
    @Mapping(target = "banner", expression = "java(getAttributeValueByName(eventEntity, \"banner\"))")
    @Mapping(target = "title", expression = "java(getAttributeValueByName(eventEntity, \"title\"))")
    @Mapping(target = "minimumAge", expression = "java(getAttributeValueByName(eventEntity, \"minimum-age\"))")
    @Mapping(target = "musicStyleList", expression = "java(getAttributeValueListByName(eventEntity, \"music-style\"))")
    @Mapping(target = "description", expression = "java(getAttributeValueByName(eventEntity, \"description\"))")
    @Mapping(target = "lineup", expression = "java(getAttributeValueByName(eventEntity, \"lineup\"))")
    @Mapping(target = "reel", expression = "java(getAttributeValueByName(eventEntity, \"reel\"))")
    // Add the custom conversion methods for startDatetime and endDatetime
    @Mapping(target = "startDatetime", source = "eventEntity.startDatetime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "endDatetime", source = "eventEntity.endDatetime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "bookingDeadline", source = "eventEntity.bookingDeadline", qualifiedByName = "instantToOffsetDateTime")
    //custom venue info logic
    @Mapping(target = "venueName", expression = "java(getVenueName(eventEntity, venueDTO))")
    @Mapping(target = "coordinates", expression = "java(getCoordinates(eventEntity, venueDTO))")
    @Mapping(target = "timeZone", expression = "java(getVenueTimeZone(venueDTO))")
    @Mapping(target = "currencyIso", expression = "java(getCurrencyIso(venueDTO))")
    EventDetailsDTO mapToEventDetailsDTO(EventEntity eventEntity, @Context VenueDTO venueDTO);

    default EventDetailsDTO mapToUserEventDetailsDTO(EventEntity eventEntity, @Context VenueDTO venueDTO) {
        return mapToEventDetailsDTO(eventEntity, venueDTO);
    }

    @Mapping(target = "title", expression = "java(getAttributeValueByName(eventEntity, \"title\"))")
    @Mapping(target = "startDatetime", source = "eventEntity.startDatetime", qualifiedByName = "instantToOffsetDateTime")
    @Mapping(target = "banner", expression = "java(getAttributeValueByName(eventEntity, \"banner\"))")
    @Mapping(target = "displayPrice", source = "eventEntity.displayPrice")
    //custom venue info logic
    @Mapping(target = "venueName", expression = "java(getVenueName(eventEntity, venueOverviewDTO))")
    @Mapping(target = "cityName", expression = "java(getCityName(eventEntity, venueOverviewDTO))")
    @Mapping(target = "countryName", expression = "java(getCountryName(eventEntity, venueOverviewDTO))")
    @Mapping(target = "timeZone", expression = "java(getVenueTimeZone(venueOverviewDTO))")
    @Mapping(target = "currencyIso", expression = "java(getCurrencyIso(venueOverviewDTO))")
    EventBrowseDTO mapToEventsBrowserDTO(EventEntity eventEntity, @Context VenueOverviewDTO venueOverviewDTO);

    @Mapping(target = "id", source = "event.id")
    @Mapping(target = "displayPrice", source = "event.displayPrice")
    @Mapping(target = "startDatetime", source = "event.startDatetime")
    @Mapping(target = "endDatetime", source = "event.endDatetime")
    @Mapping(target = "status", source = "event.status")
    @Mapping(target = "eventBanner", expression = "java(getAttributeValueByName(event, \"banner\"))")
    @Mapping(target = "eventName", expression = "java(getAttributeValueByName(event, \"title\"))")
    @Mapping(target = "timeZone", expression = "java(getVenueTimeZone(venueDTO))")
    EventVenueDTO toEventVenueDTO(EventEntity event, @Context VenueDTO venueDTO);

    default String getVenueTimeZone( VenueDTO venueDTO) {
        return Optional.ofNullable(venueDTO).map(VenueDTO::timeZone).orElse(null);
    }

    default String getVenueTimeZone( VenueOverviewDTO venueOverviewDTO) {
        return Optional.ofNullable(venueOverviewDTO).map(VenueOverviewDTO::timeZone).orElse(null);
    }

    default String getAttributeValueByName(EventEntity eventEntity, String name) {
        return getAttributeValueListByName(eventEntity, name).stream().findFirst().orElse(null);
    }

    default List<String> getAttributeValueListByName(EventEntity eventEntity, String name) {
        List<EventAttributeEntity> eventAttributeEntities = eventEntity.getEventAttributes();

        return eventAttributeEntities.stream()
                .filter(e -> name.equals(e.getAttribute().getName()))
                .map(EventAttributeEntity::getAttributeValue)
                .toList();
    }
    default String getCityName(EventEntity eventEntity, VenueOverviewDTO venueOverviewDTO) {
        if (Boolean.TRUE.equals(eventEntity.getUseCustomAddress()) && eventEntity.getCustomAddress() != null) {
            return eventEntity.getCustomAddress().getCity();
        }
        return venueOverviewDTO != null && venueOverviewDTO.companyAddress()!=null ? venueOverviewDTO.companyAddress().city() : null;
    }
    default String getCountryName(EventEntity eventEntity, VenueOverviewDTO venueOverviewDTO) {
        if (Boolean.TRUE.equals(eventEntity.getUseCustomAddress()) && eventEntity.getCustomAddress() != null) {
            return eventEntity.getCustomAddress().getCountry();
        }
        return venueOverviewDTO != null && venueOverviewDTO.companyAddress()!=null ? venueOverviewDTO.companyAddress().country().name() : null;
    }

    default String getVenueName(EventEntity eventEntity, VenueOverviewDTO venueOverviewDTO) {
        if (Boolean.TRUE.equals(eventEntity.getUseCustomAddress()) && eventEntity.getCustomAddress() != null) {
            return eventEntity.getCustomAddress().getAddressName();
        }
        return venueOverviewDTO != null ? venueOverviewDTO.name() : null;
    }

    default String getVenueName(EventEntity eventEntity, VenueDTO venueDTO) {
        if (Boolean.TRUE.equals(eventEntity.getUseCustomAddress()) && eventEntity.getCustomAddress() != null) {
            return eventEntity.getCustomAddress().getAddressName();
        }
        return venueDTO != null ? venueDTO.name() : null;
    }

    default Coordinates getCoordinates(EventEntity eventEntity, VenueDTO venueDTO) {
        if (Boolean.TRUE.equals(eventEntity.getUseCustomAddress()) && eventEntity.getCustomAddress() != null) {
            return Coordinates.builder()
                    .latitude(eventEntity.getCustomAddress().getLatitude())
                    .longitude(eventEntity.getCustomAddress().getLongitude())
                    .build();
        }
        return venueDTO != null ? venueDTO.coordinates() : null;
    }

    default String getCurrencyIso(VenueOverviewDTO venueOverviewDTO) {
        return venueOverviewDTO != null ? venueOverviewDTO.currencyIsoCode() : null;
    }

    default String getCurrencyIso(VenueDTO venueDTO) {
        return venueDTO != null ? venueDTO.currency().isoCode() : null;
    }
}